from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app import db
from app.models.company_culture import CompanyCulture
from app.models.question import Question
from app.models.learning_record import LearningRecord
from app.models.answer_record import AnswerRecord
from datetime import datetime

bp = Blueprint('learner', __name__)


@bp.route('/dashboard')
@login_required
def dashboard():
    """员工仪表盘，显示待学习的企业文化和已完成的学习"""
    # 获取员工所在酒店的企业文化
    hotel_id = current_user.department.hotel_id
    cultures = CompanyCulture.query.filter_by(hotel_id=hotel_id, is_active=True).all()

    # 检查学习记录
    learning_status = []
    for culture in cultures:
        record = LearningRecord.query.filter_by(
            learner_id=current_user.id,
            culture_id=culture.id
        ).first()
        learning_status.append({
            'culture': culture,
            'is_completed': record.is_completed if record else False,
            'record_id': record.id if record else None
        })

    return render_template('learner/dashboard.html', learning_status=learning_status)


@bp.route('/learn/<int:culture_id>')
@login_required
def learn_culture(culture_id):
    """学习企业文化内容"""
    culture = CompanyCulture.query.get_or_404(culture_id)

    # 检查是否有权限学习（属于同一酒店）
    if culture.hotel_id != current_user.department.hotel_id:
        flash('没有权限访问此内容', 'danger')
        return redirect(url_for('learner.dashboard'))

    # 创建或更新学习记录
    record = LearningRecord.query.filter_by(
        learner_id=current_user.id,
        culture_id=culture_id
    ).first()

    if not record:
        record = LearningRecord(
            learner_id=current_user.id,
            culture_id=culture_id,
            start_time=datetime.utcnow()
        )
        db.session.add(record)
    elif not record.start_time:
        record.start_time = datetime.utcnow()

    db.session.commit()

    return render_template('learner/learn.html', culture=culture, record=record)


@bp.route('/complete_learning/<int:record_id>', methods=['POST'])
@login_required
def complete_learning(record_id):
    """标记学习完成"""
    record = LearningRecord.query.get_or_404(record_id)

    if record.learner_id != current_user.id:
        flash('无权操作', 'danger')
        return redirect(url_for('learner.dashboard'))

    record.is_completed = True
    record.complete_time = datetime.utcnow()
    db.session.commit()

    flash('学习完成！可以开始答题了', 'success')
    return redirect(url_for('learner.take_quiz', culture_id=record.culture_id))


@bp.route('/quiz/<int:culture_id>')
@login_required
def take_quiz(culture_id):
    """显示企业文化对应的试题"""
    # 获取该企业文化下的所有题目
    questions = Question.query.filter_by(
        culture_id=culture_id,
        is_active=True
    ).all()

    if not questions:
        flash('暂无相关试题', 'info')
        return redirect(url_for('learner.dashboard'))

    return render_template('learner/quiz.html', questions=questions, culture_id=culture_id)


@bp.route('/submit_answers/<int:culture_id>', methods=['POST'])
@login_required
def submit_answers(culture_id):
    """提交答案并评分"""
    questions = Question.query.filter_by(culture_id=culture_id, is_active=True).all()
    total_score = 0
    obtained_score = 0

    for question in questions:
        # 获取用户答案
        if question.question_type in [1, 2]:  # 单选或多选
            user_answer = request.form.get(f'question_{question.id}')
        else:  # 简答
            user_answer = request.form.get(f'question_{question.id}', '')

        # 检查是否已存在答题记录
        answer_record = AnswerRecord.query.filter_by(
            learner_id=current_user.id,
            question_id=question.id
        ).first()

        if not answer_record:
            answer_record = AnswerRecord(
                learner_id=current_user.id,
                question_id=question.id
            )

        # 更新答题记录
        answer_record.learner_answer = user_answer
        answer_record.answer_time = datetime.utcnow()

        # 判断答案是否正确
        if question.question_type in [1, 2]:  # 单选或多选
            is_correct = (user_answer == question.correct_answer)
            answer_record.is_correct = is_correct
            answer_record.score_obtained = question.score if is_correct else 0
        else:  # 简答需要人工批改
            answer_record.is_correct = None
            answer_record.score_obtained = 0

        db.session.add(answer_record)
        total_score += question.score
        obtained_score += answer_record.score_obtained

    db.session.commit()

    return render_template('learner/quiz_result.html',
                           total_score=total_score,
                           obtained_score=obtained_score)
