from datetime import datetime
from app import db


class Department(db.Model):
    __tablename__ = 'department'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    hotel_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON><PERSON>('hotel.id'), nullable=False)
    name = db.Column(db.String(50), nullable=False)
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    reserved1 = db.Column(db.String(100))
    reserved2 = db.Column(db.Text)

    # 复合唯一约束
    __table_args__ = (
        db.UniqueConstraint('hotel_id', 'name', name='uk_hotel_department'),
    )

    # 关系定义
    learners = db.relationship('Learner', backref='department', lazy=True, cascade="all, delete-orphan")

    def __repr__(self):
        return f'<Department {self.name} in Hotel {self.hotel_id}>'
