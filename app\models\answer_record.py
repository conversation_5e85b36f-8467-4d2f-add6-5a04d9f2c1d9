from datetime import datetime
from app import db


class AnswerRecord(db.Model):
    __tablename__ = 'answer_record'

    id = db.<PERSON>umn(db.<PERSON>teger, primary_key=True)
    learner_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('learner.id'), nullable=False)
    question_id = db.<PERSON>umn(db.<PERSON>ger, db.<PERSON><PERSON>('question.id'), nullable=False)
    learner_answer = db.<PERSON>umn(db.Text)
    is_correct = db.Column(db.<PERSON><PERSON>, nullable=True)  # None表示未批改
    score_obtained = db.Column(db.Integer, default=0)
    answer_time = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    reserved1 = db.Column(db.String(100))
    reserved2 = db.Column(db.Text)

    # 复合唯一约束
    __table_args__ = (
        db.Unique<PERSON>onstraint('learner_id', 'question_id', name='uk_learner_question'),
    )

    def __repr__(self):
        return f'<AnswerRecord Learner {self.learner_id} - Question {self.question_id}>'
