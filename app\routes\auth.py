from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from app import db
from app.models.learner import Learner

bp = Blueprint('auth', __name__)


@bp.route('/login', methods=['GET', 'POST'])
def login():
    """员工登录"""
    if request.method == 'POST':
        employee_id = request.form.get('employee_id')
        password = request.form.get('password')  # 实际项目中应使用密码字段

        # 查找员工
        learner = Learner.query.filter_by(employee_id=employee_id, status=1).first()

        if not learner:
            flash('员工工号不存在或已离职', 'danger')
            return redirect(url_for('auth.login'))

        # 注意：实际项目中应使用密码哈希验证，这里简化处理
        # 假设密码存储在reserved1字段（仅示例，实际应单独设计密码字段）
        if not check_password_hash(learner.reserved1 or '', password):
            flash('密码错误', 'danger')
            return redirect(url_for('auth.login'))

        # 登录用户
        login_user(learner)
        next_page = request.args.get('next')
        return redirect(next_page or url_for('learner.dashboard'))

    return render_template('auth/login.html')


@bp.route('/logout')
@login_required
def logout():
    """员工登出"""
    logout_user()
    flash('已成功登出', 'info')
    return redirect(url_for('auth.login'))


@bp.route('/profile')
@login_required
def profile():
    """查看/编辑个人资料"""
    return render_template('auth/profile.html')


@bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """修改密码"""
    if request.method == 'POST':
        old_password = request.form.get('old_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        # 验证密码
        if new_password != confirm_password:
            flash('两次输入的新密码不一致', 'danger')
            return redirect(url_for('auth.change_password'))

        # 验证旧密码
        if not check_password_hash(current_user.reserved1 or '', old_password):
            flash('旧密码错误', 'danger')
            return redirect(url_for('auth.change_password'))

        # 更新密码（实际项目中应使用哈希存储）
        from werkzeug.security import generate_password_hash
        current_user.reserved1 = generate_password_hash(new_password)
        db.session.commit()

        flash('密码已更新，请重新登录', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/change_password.html')
