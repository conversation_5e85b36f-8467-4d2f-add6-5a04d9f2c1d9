from datetime import datetime
from app import db


class CompanyCulture(db.Model):
    __tablename__ = 'company_culture'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    hotel_id = db.Column(db.Integer, db.<PERSON>ey('hotel.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    content = db.Column(db.Text, nullable=False)
    is_active = db.Column(db.Bo<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    reserved1 = db.Column(db.String(100))
    reserved2 = db.Column(db.Text)

    # 关系定义
    questions = db.relationship('Question', backref='company_culture', lazy=True, cascade="all, delete-orphan")
    learning_records = db.relationship('LearningRecord', backref='company_culture', lazy=True,
                                       cascade="all, delete-orphan")

    def __repr__(self):
        return f'<CompanyCulture {self.title} (Hotel: {self.hotel_id})>'
