from datetime import datetime
from app import db
import json


class Question(db.Model):
    __tablename__ = 'question'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    culture_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('company_culture.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    question_type = db.Column(db.Integer, nullable=False)  # 1:单选 2:多选 3:简答
    options = db.Column(db.JSON)  # 存储选项的JSON数据
    correct_answer = db.Column(db.Text, nullable=False)
    score = db.Column(db.Integer, default=10)
    explanation = db.Column(db.Text)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    reserved1 = db.Column(db.String(100))
    reserved2 = db.Column(db.Text)

    # 关系定义
    answer_records = db.relationship('AnswerRecord', backref='question', lazy=True, cascade="all, delete-orphan")

    def get_options(self):
        """将JSON选项转换为Python列表"""
        if self.options:
            return self.options
        return []

    def __repr__(self):
        return f'<Question {self.id} (Type: {self.question_type})>'
