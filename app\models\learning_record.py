from datetime import datetime
from app import db


class LearningRecord(db.Model):
    __tablename__ = 'learning_record'

    id = db.<PERSON>umn(db.<PERSON>te<PERSON>, primary_key=True)
    learner_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('learner.id'), nullable=False)
    culture_id = db.Column(db.<PERSON>ger, db.<PERSON>ey('company_culture.id'), nullable=False)
    is_completed = db.Column(db.<PERSON>, default=False)
    start_time = db.Column(db.DateTime)
    complete_time = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    reserved1 = db.Column(db.String(100))
    reserved2 = db.Column(db.Text)

    # 复合唯一约束
    __table_args__ = (
        db.Unique<PERSON>onstraint('learner_id', 'culture_id', name='uk_learner_culture'),
    )

    def __repr__(self):
        status = "completed" if self.is_completed else "in progress"
        return f'<LearningRecord Learner {self.learner_id} - Culture {self.culture_id} ({status})>'
