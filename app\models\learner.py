from datetime import datetime
from app import db, login_manager
from flask_login import UserMixin


class Learner(UserMixin, db.Model):
    __tablename__ = 'learner'

    id = db.Column(db.Integer, primary_key=True)
    department_id = db.Column(db.<PERSON><PERSON>, db.<PERSON><PERSON>('department.id'), nullable=False)
    name = db.Column(db.String(50), nullable=False)
    employee_id = db.Column(db.String(20), unique=True)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    status = db.Column(db.<PERSON>, default=True)  # 1:在职 0:离职
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    reserved1 = db.Column(db.String(100))
    reserved2 = db.Column(db.Text)

    # 关系定义
    learning_records = db.relationship('LearningRecord', backref='learner', lazy=True, cascade="all, delete-orphan")
    answer_records = db.relationship('AnswerRecord', backref='learner', lazy=True, cascade="all, delete-orphan")

    def get_id(self):
        return str(self.id)

    def __repr__(self):
        return f'<Learner {self.name} (ID: {self.employee_id})>'


@login_manager.user_loader
def load_user(user_id):
    return Learner.query.get(int(user_id))
