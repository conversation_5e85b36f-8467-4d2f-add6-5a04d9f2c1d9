<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="4">
            <item index="0" class="java.lang.String" itemvalue="argon2-cffi" />
            <item index="1" class="java.lang.String" itemvalue="asgiref" />
            <item index="2" class="java.lang.String" itemvalue="cryptography" />
            <item index="3" class="java.lang.String" itemvalue="Flask" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>