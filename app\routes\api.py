from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from app import db
from app.models.hotel import Hotel
from app.models.department import Department
from app.models.learner import Learner
from app.models.company_culture import CompanyCulture
from app.models.question import Question
from app.models.learning_record import LearningRecord
from app.models.answer_record import AnswerRecord
import json

bp = Blueprint('api', __name__)


# 基础API响应格式化
def api_response(success=True, data=None, message=''):
    return jsonify({
        'success': success,
        'data': data,
        'message': message
    })


# 酒店相关API
@bp.route('/hotels')
@login_required
def api_hotels():
    """获取酒店列表API"""
    hotels = Hotel.query.all()
    data = [{
        'id': h.id,
        'name': h.name,
        'address': h.address,
        'phone': h.phone,
        'created_at': h.created_at.isoformat()
    } for h in hotels]
    return api_response(data=data)


# 部门相关API
@bp.route('/departments/<int:hotel_id>')
@login_required
def api_departments(hotel_id):
    """获取指定酒店的部门列表"""
    departments = Department.query.filter_by(hotel_id=hotel_id).all()
    data = [{
        'id': d.id,
        'name': d.name,
        'description': d.description
    } for d in departments]
    return api_response(data=data)


# 员工相关API
@bp.route('/learners/<int:dept_id>')
@login_required
def api_learners(dept_id):
    """获取指定部门的员工列表"""
    learners = Learner.query.filter_by(department_id=dept_id).all()
    data = [{
        'id': l.id,
        'name': l.name,
        'employee_id': l.employee_id,
        'phone': l.phone,
        'email': l.email,
        'status': l.status
    } for l in learners]
    return api_response(data=data)


# 企业文化相关API
@bp.route('/cultures/<int:hotel_id>')
@login_required
def api_cultures(hotel_id):
    """获取指定酒店的企业文化列表"""
    cultures = CompanyCulture.query.filter_by(hotel_id=hotel_id, is_active=True).all()
    data = [{
        'id': c.id,
        'title': c.title,
        'content': c.content,
        'created_at': c.created_at.isoformat()
    } for c in cultures]
    return api_response(data=data)


# 学习记录API
@bp.route('/learning/record', methods=['POST'])
@login_required
def api_learning_record():
    """创建或更新学习记录"""
    data = request.json
    culture_id = data.get('culture_id')

    if not culture_id:
        return api_response(success=False, message='缺少企业文化ID')

    # 检查企业文化是否存在
    culture = CompanyCulture.query.get(culture_id)
    if not culture:
        return api_response(success=False, message='企业文化不存在')

    # 检查权限（同一酒店）
    if culture.hotel_id != current_user.department.hotel_id:
        return api_response(success=False, message='没有权限访问此内容')

    # 创建或更新学习记录
    record = LearningRecord.query.filter_by(
        learner_id=current_user.id,
        culture_id=culture_id
    ).first()

    if not record:
        record = LearningRecord(
            learner_id=current_user.id,
            culture_id=culture_id,
            start_time=data.get('start_time')
        )
        db.session.add(record)
    else:
        if 'is_completed' in data:
            record.is_completed = data['is_completed']
            if data['is_completed'] and not record.complete_time:
                from datetime import datetime
                record.complete_time = datetime.utcnow()

    db.session.commit()

    return api_response(data={'record_id': record.id})


# 答题API
@bp.route('/answer/submit', methods=['POST'])
@login_required
def api_submit_answer():
    """提交答案"""
    data = request.json
    question_id = data.get('question_id')
    learner_answer = data.get('learner_answer')

    if not question_id or learner_answer is None:
        return api_response(success=False, message='缺少参数')

    # 检查问题是否存在
    question = Question.query.get(question_id)
    if not question:
        return api_response(success=False, message='问题不存在')

    # 检查是否已答题
    answer_record = AnswerRecord.query.filter_by(
        learner_id=current_user.id,
        question_id=question_id
    ).first()

    if not answer_record:
        answer_record = AnswerRecord(
            learner_id=current_user.id,
            question_id=question_id
        )

    # 更新答题记录
    from datetime import datetime
    answer_record.learner_answer = learner_answer
    answer_record.answer_time = datetime.utcnow()

    # 自动批改（单选/多选）
    if question.question_type in [1, 2]:
        is_correct = (learner_answer == question.correct_answer)
        answer_record.is_correct = is_correct
        answer_record.score_obtained = question.score if is_correct else 0

    db.session.commit()

    return api_response(data={
        'id': answer_record.id,
        'is_correct': answer_record.is_correct,
        'score_obtained': answer_record.score_obtained
    })


# 学习统计API
@bp.route('/stats/learning')
@login_required
def api_learning_stats():
    """获取学习统计数据"""
    # 检查是否为管理员
    is_admin = current_user.employee_id == 'admin'

    if is_admin:
        # 管理员：统计所有酒店
        hotel_stats = []
        hotels = Hotel.query.all()
        for hotel in hotels:
            # 企业文化总数
            culture_count = CompanyCulture.query.filter_by(hotel_id=hotel.id).count()
            # 员工总数
            learner_count = Learner.query.join(Department).filter(
                Department.hotel_id == hotel.id
            ).count()
            # 已完成学习的员工数
            completed_count = LearningRecord.query.join(CompanyCulture).filter(
                CompanyCulture.hotel_id == hotel.id,
                LearningRecord.is_completed == True
            ).distinct(LearningRecord.learner_id).count()

            hotel_stats.append({
                'hotel_id': hotel.id,
                'hotel_name': hotel.name,
                'culture_count': culture_count,
                'learner_count': learner_count,
                'completed_count': completed_count,
                'completion_rate': completed_count / learner_count if learner_count > 0 else 0
            })
        return api_response(data=hotel_stats)
    else:
        # 普通员工：统计个人学习情况
        records = LearningRecord.query.filter_by(learner_id=current_user.id).all()
        total = len(records)
        completed = sum(1 for r in records if r.is_completed)

        # 答题情况
        answer_stats = {
            'total_questions': AnswerRecord.query.filter_by(learner_id=current_user.id).count(),
            'correct_questions': AnswerRecord.query.filter_by(
                learner_id=current_user.id,
                is_correct=True
            ).count()
        }

        return api_response(data={
            'total_cultures': total,
            'completed_cultures': completed,
            'completion_rate': completed / total if total > 0 else 0,
            'answer_stats': answer_stats
        })
