from app import create_app, db
from app.models import hotel, department, learner, company_culture, question, learning_record, answer_record

app = create_app()

@app.shell_context_processor
def make_shell_context():
    return {
        'db': db,
        'Hotel': hotel.Hotel,
        'Department': department.Department,
        'Learner': learner.Learner,
        'CompanyCulture': company_culture.CompanyCulture,
        'Question': question.Question,
        'LearningRecord': learning_record.LearningRecord,
        'AnswerRecord': answer_record.AnswerRecord
    }

if __name__ == '__main__':
    app.run(debug=True)