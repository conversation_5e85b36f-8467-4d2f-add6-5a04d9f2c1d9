from datetime import datetime
from app import db


class Hotel(db.Model):
    __tablename__ = 'hotel'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    address = db.Column(db.String(255))
    phone = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    reserved1 = db.Column(db.String(100))
    reserved2 = db.Column(db.Text)

    # 关系定义
    departments = db.relationship('Department', backref='hotel', lazy=True, cascade="all, delete-orphan")
    company_cultures = db.relationship('CompanyCulture', backref='hotel', lazy=True, cascade="all, delete-orphan")

    def __repr__(self):
        return f'<Hotel {self.name}>'
